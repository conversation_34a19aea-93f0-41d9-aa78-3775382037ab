rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function isAdmin() {
      return request.auth.token.email == '<EMAIL>';
    }
    
    function isGroupMember(groupId) {
      return request.auth.uid in get(/databases/$(database)/documents/splitwise-groups/$(groupId)).data.members;
    }

    // Users collection
    match /splitwise-users/{userId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && (isOwner(userId) || isAdmin());
      allow create: if isAuthenticated() && isOwner(userId);
    }

    // Groups collection
    match /splitwise-groups/{groupId} {
      allow read: if isAuthenticated() && (isGroupMember(groupId) || isAdmin());
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (isGroupMember(groupId) || isAdmin());
      allow delete: if isAdmin();
    }
    
    // Expenses collection
    match /splitwise-expenses/{expenseId} {
      allow read: if isAuthenticated() && (
        resource.data.paidBy == request.auth.uid ||
        request.auth.uid in resource.data.splitBetween ||
        isAdmin()
      );
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (
        resource.data.paidBy == request.auth.uid ||
        isAdmin()
      );
      allow delete: if isAuthenticated() && (
        resource.data.paidBy == request.auth.uid ||
        isAdmin()
      );
    }

    // Personal expenses collection
    match /splitwise-personalExpenses/{expenseId} {
      allow read, write: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isAdmin()
      );
      allow create: if isAuthenticated();
    }
    
    // Activity logs collection
    match /splitwise-activityLogs/{logId} {
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isAdmin()
      );
      allow create: if isAuthenticated();
      allow update, delete: if isAdmin();
    }

    // Balances collection
    match /splitwise-balances/{balanceId} {
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        isAdmin()
      );
      allow write: if isAuthenticated();
    }

    // Statistics collection
    match /splitwise-statistics/{userId} {
      allow read, write: if isAuthenticated() && (
        isOwner(userId) ||
        isAdmin()
      );
    }
    
    // Admin-only collections
    match /adminData/{document=**} {
      allow read, write: if isAdmin();
    }
  }
}
