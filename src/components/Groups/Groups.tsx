import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Plus, Users, Calendar } from 'lucide-react';
import { useGroups } from '../../hooks/useGroups';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../UI/LoadingSpinner';
import CreateGroupModal from './CreateGroupModal';

const Groups: React.FC = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { groups, loading, error, createGroup } = useGroups();
  const [showCreateForm, setShowCreateForm] = useState(false);

  if (loading) {
    return <LoadingSpinner text="Loading groups..." />;
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 dark:text-red-400">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Groups
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            Manage your group expenses and split bills with friends
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <Plus size={20} />
          Create Group
        </button>
      </div>

      {/* Groups Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {groups.map((group) => (
          <div key={group.id} className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] border border-gray-200 dark:border-gray-700 overflow-hidden cursor-pointer">
            {/* Group Header */}
            <div className="p-6 border-b border-gray-100 dark:border-gray-700">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <Users size={20} className="text-white" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900 dark:text-white text-lg">{group.name}</h3>
                    {group.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{group.description}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Group Stats */}
            <div className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Expenses</span>
                <div className="flex items-center gap-1 text-green-600 dark:text-green-400 font-bold text-lg">
                  <span className="text-green-600 dark:text-green-400">₹</span>
                  {group.totalExpenses.toLocaleString()}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Members</span>
                <div className="flex items-center gap-1">
                  <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">{group.members.length}</span>
                  <Users size={14} className="text-blue-600 dark:text-blue-400" />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Created</span>
                <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400">
                  <Calendar size={14} />
                  {group.createdAt.toLocaleDateString()}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="p-6 pt-0">
              <div className="flex gap-3">
                <button
                  onClick={() => navigate(`/groups/${group.id}`)}
                  className="flex-1 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 py-3 px-4 rounded-xl text-sm font-semibold hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-all duration-200 hover:scale-105 border border-blue-200 dark:border-blue-800"
                >
                  View Details
                </button>
                <button
                  onClick={() => navigate(`/groups/${group.id}`)}
                  className="flex-1 bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400 py-3 px-4 rounded-xl text-sm font-semibold hover:bg-green-100 dark:hover:bg-green-900/50 transition-all duration-200 hover:scale-105 border border-green-200 dark:border-green-800"
                >
                  Add Expense
                </button>
              </div>
            </div>
          </div>
        ))}

        {/* Empty state */}
        {groups.length === 0 && (
          <div className="col-span-full">
            <div className="text-center py-16 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl border-2 border-dashed border-blue-200 dark:border-gray-600">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Users size={32} className="text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">No groups yet</h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md mx-auto">
                Create your first group to start tracking expenses with friends and family. Split bills, track spending, and keep everyone in sync!
              </p>
              <button
                onClick={() => setShowCreateForm(true)}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <div className="flex items-center gap-2">
                  <Plus size={20} />
                  Create Your First Group
                </div>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Create Group Modal */}
      {showCreateForm && (
        <CreateGroupModal
          onClose={() => setShowCreateForm(false)}
          onCreateGroup={createGroup}
          currentUserId={currentUser?.uid || ''}
        />
      )}
    </div>
  );
};

export default Groups;
