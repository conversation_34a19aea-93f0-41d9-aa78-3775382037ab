import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Users, Plus, Settings, MoreVertical, Receipt, Calendar, User } from 'lucide-react';
import { useGroups } from '../../hooks/useGroups';
import { useExpenses } from '../../hooks/useExpenses';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../UI/LoadingSpinner';
import type { Group, Expense } from '../../types';

const GroupDetails: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { groups, loading: groupsLoading } = useGroups();
  const { expenses, loading: expensesLoading } = useExpenses();
  const [group, setGroup] = useState<Group | null>(null);
  const [groupExpenses, setGroupExpenses] = useState<Expense[]>([]);
  const [showAddExpense, setShowAddExpense] = useState(false);

  useEffect(() => {
    if (groupId && groups.length > 0) {
      const foundGroup = groups.find(g => g.id === groupId);
      setGroup(foundGroup || null);
    }
  }, [groupId, groups]);

  useEffect(() => {
    if (groupId && expenses.length > 0) {
      const filteredExpenses = expenses.filter(expense => expense.groupId === groupId);
      setGroupExpenses(filteredExpenses);
    }
  }, [groupId, expenses]);

  if (groupsLoading || expensesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size={48} text="Loading group details..." />
      </div>
    );
  }

  if (!group) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Group Not Found</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">The group you're looking for doesn't exist or you don't have access to it.</p>
            <button
              onClick={() => navigate('/groups')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              Back to Groups
            </button>
          </div>
        </div>
      </div>
    );
  }

  const isCreator = group.createdBy === currentUser?.uid;
  const isMember = group.members.includes(currentUser?.uid || '');

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/groups')}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <ArrowLeft size={20} className="text-gray-600 dark:text-gray-300" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{group.name}</h1>
                {group.description && (
                  <p className="text-gray-600 dark:text-gray-300 mt-1">{group.description}</p>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={() => setShowAddExpense(true)}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Plus size={18} />
                Add Expense
              </button>
              {isCreator && (
                <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                  <Settings size={20} className="text-gray-600 dark:text-gray-300" />
                </button>
              )}
              <button className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                <MoreVertical size={20} className="text-gray-600 dark:text-gray-300" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Group Stats */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Group Summary</h2>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    ₹{group.totalExpenses.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-300 mt-1">Total Expenses</div>
                </div>
                <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {groupExpenses.length}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-300 mt-1">Total Transactions</div>
                </div>
              </div>
            </div>

            {/* Recent Expenses */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Recent Expenses</h2>
                <button className="text-blue-600 dark:text-blue-400 hover:underline text-sm">
                  View All
                </button>
              </div>
              
              {groupExpenses.length === 0 ? (
                <div className="text-center py-8">
                  <Receipt size={48} className="mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Expenses Yet</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Start by adding your first group expense
                  </p>
                  <button
                    onClick={() => setShowAddExpense(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    Add First Expense
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  {groupExpenses.slice(0, 5).map((expense) => (
                    <div key={expense.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                          <Receipt size={18} className="text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">{expense.title}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-300">
                            {expense.date.toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-gray-900 dark:text-white">
                          ₹{expense.amount.toLocaleString()}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">
                          Split {expense.splitType}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Group Members */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Members</h2>
                <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
                  <Users size={16} />
                  <span className="text-sm font-medium">{group.members.length}</span>
                </div>
              </div>
              
              <div className="space-y-3">
                {group.members.map((memberId, index) => (
                  <div key={memberId} className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                      <User size={16} className="text-gray-600 dark:text-gray-300" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {memberId === currentUser?.uid ? 'You' : `Member ${index + 1}`}
                        {memberId === group.createdBy && (
                          <span className="ml-2 text-xs bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 px-2 py-1 rounded">
                            Creator
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        Balance: ₹0
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {isCreator && (
                <button className="w-full mt-4 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 py-2 px-4 rounded-lg text-sm font-medium hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors">
                  Add Members
                </button>
              )}
            </div>

            {/* Group Info */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Group Info</h2>
              <div className="space-y-3 text-sm">
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                  <Calendar size={16} />
                  <span>Created {group.createdAt.toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                  <User size={16} />
                  <span>Created by {group.createdBy === currentUser?.uid ? 'You' : 'Group Creator'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Expense Modal Placeholder */}
      {showAddExpense && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Add Expense</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Expense creation modal will be implemented next.
            </p>
            <button
              onClick={() => setShowAddExpense(false)}
              className="w-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GroupDetails;
