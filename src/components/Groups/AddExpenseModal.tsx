import React, { useState } from 'react';
import { X, Plus, Minus, Users, Calculator, Per<PERSON>, Hash, DollarSign } from 'lucide-react';
import { EXPENSE_CATEGORIES, getCategoryIcon } from '../../services/expenseService';
import type { ExpenseCategory } from '../../types';

interface AddExpenseModalProps {
  onClose: () => void;
  onAddExpense: (expenseData: any) => Promise<void>;
  groupMembers: string[];
  currentUserId: string;
  groupId: string;
}

type SplitType = 'equal' | 'exact' | 'percentage' | 'shares';

const AddExpenseModal: React.FC<AddExpenseModalProps> = ({
  onClose,
  onAddExpense,
  groupMembers,
  currentUserId,
  groupId
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    amount: '',
    category: 'other' as ExpenseCategory,
    date: new Date().toISOString().split('T')[0]
  });
  
  const [splitType, setSplitType] = useState<SplitType>('equal');
  const [paidBy, setPaidBy] = useState(currentUserId);
  const [splitBetween, setSplitBetween] = useState<string[]>(groupMembers);
  const [splitDetails, setSplitDetails] = useState<{ [userId: string]: number }>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const totalAmount = parseFloat(formData.amount) || 0;

  // Calculate split amounts based on type
  const calculateSplitAmounts = () => {
    const selectedMembers = splitBetween;
    const amounts: { [userId: string]: number } = {};

    switch (splitType) {
      case 'equal':
        const equalAmount = totalAmount / selectedMembers.length;
        selectedMembers.forEach(memberId => {
          amounts[memberId] = equalAmount;
        });
        break;
      case 'exact':
      case 'percentage':
      case 'shares':
        selectedMembers.forEach(memberId => {
          amounts[memberId] = splitDetails[memberId] || 0;
        });
        break;
    }
    return amounts;
  };

  const handleSplitDetailChange = (memberId: string, value: string) => {
    const numValue = parseFloat(value) || 0;
    setSplitDetails(prev => ({
      ...prev,
      [memberId]: numValue
    }));
  };

  const validateSplit = () => {
    if (splitType === 'equal') return true;
    
    const amounts = Object.values(splitDetails);
    const total = amounts.reduce((sum, amount) => sum + amount, 0);
    
    if (splitType === 'exact') {
      return Math.abs(total - totalAmount) < 0.01;
    } else if (splitType === 'percentage') {
      return Math.abs(total - 100) < 0.01;
    }
    return total > 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.amount || totalAmount <= 0) {
      setError('Please fill in all required fields');
      return;
    }

    if (splitBetween.length === 0) {
      setError('Please select at least one person to split with');
      return;
    }

    if (!validateSplit()) {
      setError('Split amounts do not add up correctly');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const calculatedSplits = calculateSplitAmounts();
      
      const expenseData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        amount: totalAmount,
        category: formData.category,
        type: 'group' as const,
        groupId,
        paidBy,
        splitBetween,
        splitType,
        splitDetails: calculatedSplits,
        date: new Date(formData.date),
        isSettled: false
      };

      await onAddExpense(expenseData);
      onClose();
    } catch (err) {
      setError('Failed to add expense. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const toggleMemberInSplit = (memberId: string) => {
    setSplitBetween(prev => 
      prev.includes(memberId) 
        ? prev.filter(id => id !== memberId)
        : [...prev, memberId]
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Add Expense</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X size={20} className="text-gray-600 dark:text-gray-300" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {error && (
            <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-3">
              <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Expense Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="e.g., Dinner at restaurant"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Amount (₹) *
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.amount}
                onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="0.00"
                required
              />
            </div>
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Category
            </label>
            <div className="grid grid-cols-4 md:grid-cols-6 gap-2">
              {EXPENSE_CATEGORIES.map((category) => (
                <button
                  key={category}
                  type="button"
                  onClick={() => setFormData({ ...formData, category })}
                  className={`p-2 rounded-lg border text-center transition-colors ${
                    formData.category === category
                      ? 'bg-blue-100 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300'
                      : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'
                  }`}
                >
                  <div className="text-lg mb-1">{getCategoryIcon(category)}</div>
                  <div className="text-xs capitalize">{category}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={2}
              placeholder="Optional description..."
            />
          </div>

          {/* Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Date
            </label>
            <input
              type="date"
              value={formData.date}
              onChange={(e) => setFormData({ ...formData, date: e.target.value })}
              className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Paid By */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Paid By
            </label>
            <select
              value={paidBy}
              onChange={(e) => setPaidBy(e.target.value)}
              className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {groupMembers.map((memberId) => (
                <option key={memberId} value={memberId}>
                  {memberId === currentUserId ? 'You' : `Member ${memberId.slice(-4)}`}
                </option>
              ))}
            </select>
          </div>

          {/* Split Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Split Type
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <button
                type="button"
                onClick={() => setSplitType('equal')}
                className={`p-3 rounded-lg border text-center transition-colors ${
                  splitType === 'equal'
                    ? 'bg-blue-100 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300'
                    : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'
                }`}
              >
                <Users size={20} className="mx-auto mb-1" />
                <div className="text-xs">Equal</div>
              </button>
              
              <button
                type="button"
                onClick={() => setSplitType('exact')}
                className={`p-3 rounded-lg border text-center transition-colors ${
                  splitType === 'exact'
                    ? 'bg-blue-100 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300'
                    : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'
                }`}
              >
                <Calculator size={20} className="mx-auto mb-1" />
                <div className="text-xs">Exact</div>
              </button>
              
              <button
                type="button"
                onClick={() => setSplitType('percentage')}
                className={`p-3 rounded-lg border text-center transition-colors ${
                  splitType === 'percentage'
                    ? 'bg-blue-100 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300'
                    : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'
                }`}
              >
                <Percent size={20} className="mx-auto mb-1" />
                <div className="text-xs">Percentage</div>
              </button>
              
              <button
                type="button"
                onClick={() => setSplitType('shares')}
                className={`p-3 rounded-lg border text-center transition-colors ${
                  splitType === 'shares'
                    ? 'bg-blue-100 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300'
                    : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600'
                }`}
              >
                <Hash size={20} className="mx-auto mb-1" />
                <div className="text-xs">Shares</div>
              </button>
            </div>
          </div>

          {/* Split Between Members */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Split Between
            </label>
            <div className="space-y-2">
              {groupMembers.map((memberId) => (
                <div key={memberId} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={splitBetween.includes(memberId)}
                      onChange={() => toggleMemberInSplit(memberId)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="text-gray-900 dark:text-white">
                      {memberId === currentUserId ? 'You' : `Member ${memberId.slice(-4)}`}
                    </span>
                  </div>

                  {splitBetween.includes(memberId) && splitType !== 'equal' && (
                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        step="0.01"
                        value={splitDetails[memberId] || ''}
                        onChange={(e) => handleSplitDetailChange(memberId, e.target.value)}
                        className="w-20 px-2 py-1 text-sm bg-white dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={splitType === 'percentage' ? '%' : splitType === 'shares' ? '#' : '₹'}
                      />
                      {splitType === 'percentage' && <span className="text-sm text-gray-500">%</span>}
                      {splitType === 'shares' && <span className="text-sm text-gray-500">shares</span>}
                      {splitType === 'exact' && <span className="text-sm text-gray-500">₹</span>}
                    </div>
                  )}

                  {splitBetween.includes(memberId) && splitType === 'equal' && (
                    <span className="text-sm text-gray-500">
                      ₹{(totalAmount / splitBetween.length).toFixed(2)}
                    </span>
                  )}
                </div>
              ))}
            </div>

            {/* Split Summary */}
            {splitType !== 'equal' && splitBetween.length > 0 && (
              <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                <div className="text-sm text-blue-700 dark:text-blue-300">
                  {splitType === 'exact' && (
                    <>Total: ₹{Object.values(splitDetails).reduce((sum, val) => sum + (val || 0), 0).toFixed(2)} / ₹{totalAmount.toFixed(2)}</>
                  )}
                  {splitType === 'percentage' && (
                    <>Total: {Object.values(splitDetails).reduce((sum, val) => sum + (val || 0), 0).toFixed(1)}% / 100%</>
                  )}
                  {splitType === 'shares' && (
                    <>Total Shares: {Object.values(splitDetails).reduce((sum, val) => sum + (val || 0), 0)}</>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white py-3 px-4 rounded-lg font-medium transition-colors"
              disabled={loading}
            >
              {loading ? 'Adding...' : 'Add Expense'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddExpenseModal;
