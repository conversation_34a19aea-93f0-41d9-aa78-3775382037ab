import React, { useState, useEffect } from 'react';
import { Activity, Users, Receipt, Wallet, Plus, Edit, Trash2, Calendar, Filter } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { activityService } from '../../services/firestore';
import LoadingSpinner from '../UI/LoadingSpinner';
import type { ActivityLog as ActivityLogType, ActivityAction } from '../../types';

const ActivityLog: React.FC = () => {
  const { currentUser } = useAuth();
  const [activities, setActivities] = useState<ActivityLogType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<ActivityAction | 'all'>('all');

  useEffect(() => {
    if (!currentUser) {
      setActivities([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    // Set up real-time listener
    const unsubscribe = activityService.onSnapshot(
      currentUser.uid,
      (updatedActivities) => {
        setActivities(updatedActivities);
        setLoading(false);
      }
    );

    return () => {
      unsubscribe();
    };
  }, [currentUser]);

  if (loading) {
    return <LoadingSpinner text="Loading activity log..." />;
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  const filteredActivities = filter === 'all'
    ? activities
    : activities.filter(activity => activity.action === filter);

  const getActivityIcon = (action: ActivityAction) => {
    switch (action) {
      case 'created_group':
      case 'joined_group':
      case 'left_group':
        return <Users size={16} className="text-blue-600 dark:text-blue-400" />;
      case 'added_expense':
      case 'updated_expense':
      case 'deleted_expense':
      case 'settled_expense':
        return <Receipt size={16} className="text-green-600 dark:text-green-400" />;
      case 'added_personal_expense':
      case 'updated_personal_expense':
      case 'deleted_personal_expense':
        return <Wallet size={16} className="text-purple-600 dark:text-purple-400" />;
      default:
        return <Activity size={16} className="text-gray-600 dark:text-gray-400" />;
    }
  };

  const getActivityColor = (action: ActivityAction) => {
    switch (action) {
      case 'created_group':
      case 'joined_group':
      case 'added_expense':
      case 'added_personal_expense':
        return 'bg-green-100 dark:bg-green-900/30 border-green-200 dark:border-green-800';
      case 'updated_expense':
      case 'updated_personal_expense':
        return 'bg-blue-100 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800';
      case 'deleted_expense':
      case 'deleted_personal_expense':
      case 'left_group':
        return 'bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800';
      case 'settled_expense':
        return 'bg-yellow-100 dark:bg-yellow-900/30 border-yellow-200 dark:border-yellow-800';
      default:
        return 'bg-gray-100 dark:bg-gray-700 border-gray-200 dark:border-gray-600';
    }
  };

  const formatActionText = (action: ActivityAction) => {
    switch (action) {
      case 'created_group': return 'Created Group';
      case 'joined_group': return 'Joined Group';
      case 'left_group': return 'Left Group';
      case 'added_expense': return 'Added Expense';
      case 'updated_expense': return 'Updated Expense';
      case 'deleted_expense': return 'Deleted Expense';
      case 'settled_expense': return 'Settled Expense';
      case 'added_personal_expense': return 'Added Personal Expense';
      case 'updated_personal_expense': return 'Updated Personal Expense';
      case 'deleted_personal_expense': return 'Deleted Personal Expense';
      default: return action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const actionTypes: (ActivityAction | 'all')[] = [
    'all',
    'created_group',
    'added_expense',
    'added_personal_expense',
    'updated_expense',
    'deleted_expense'
  ];

  return (
    <div className="space-y-4 pb-20">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">Activity Log</h1>
          <p className="text-sm text-gray-600 dark:text-gray-300">View all your recent activities and changes</p>
        </div>

        {/* Filter */}
        <div className="flex items-center gap-2">
          <Filter size={16} className="text-gray-600 dark:text-gray-300" />
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as ActivityAction | 'all')}
            className="px-3 py-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Activities</option>
            {actionTypes.slice(1).map((action) => (
              <option key={action} value={action}>
                {formatActionText(action as ActivityAction)}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Activities List */}
      {filteredActivities.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center">
          <Activity size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {filter === 'all' ? 'No Activities Yet' : `No ${formatActionText(filter as ActivityAction)} Activities`}
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            {filter === 'all'
              ? 'Start using the app to see your activity history here.'
              : `No ${formatActionText(filter as ActivityAction).toLowerCase()} activities found.`
            }
          </p>
        </div>
      ) : (
        <div className="space-y-3">
          {filteredActivities.map((activity) => (
            <div
              key={activity.id}
              className={`p-4 rounded-lg border ${getActivityColor(activity.action)}`}
            >
              <div className="flex items-start gap-3">
                <div className="p-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                  {getActivityIcon(activity.action)}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatActionText(activity.action)}
                    </h4>
                    <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-300">
                      <Calendar size={12} />
                      <span>{activity.timestamp.toLocaleDateString()}</span>
                      <span>{activity.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                    </div>
                  </div>

                  <p className="text-sm text-gray-700 dark:text-gray-300 mt-1">
                    {activity.details}
                  </p>

                  {activity.metadata && Object.keys(activity.metadata).length > 0 && (
                    <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
                      {activity.metadata.amount && (
                        <span className="inline-block bg-white dark:bg-gray-800 px-2 py-1 rounded border mr-2">
                          Amount: ₹{activity.metadata.amount.toLocaleString()}
                        </span>
                      )}
                      {activity.metadata.category && (
                        <span className="inline-block bg-white dark:bg-gray-800 px-2 py-1 rounded border mr-2 capitalize">
                          {activity.metadata.category}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ActivityLog;
