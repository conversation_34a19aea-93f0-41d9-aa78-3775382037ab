import React, { useState } from 'react';
import { Wallet, Plus, TrendingUp, Calendar, ChevronLeft, ChevronRight, Edit, Trash2, Tag } from 'lucide-react';
import { usePersonalExpenses } from '../../hooks/useExpenses';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../UI/LoadingSpinner';
import AddPersonalExpenseModal from './AddPersonalExpenseModal';
import { getCategoryIcon } from '../../services/expenseService';

const PersonalExpenses: React.FC = () => {
  const { currentUser } = useAuth();
  const [currentMonth, setCurrentMonth] = useState(new Date().toISOString().slice(0, 7)); // YYYY-MM format
  const { expenses, loading, error } = usePersonalExpenses(currentMonth);
  const [showAddExpense, setShowAddExpense] = useState(false);

  if (loading) {
    return <LoadingSpinner text="Loading personal expenses..." />;
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount, 0);
  const categoryTotals = expenses.reduce((acc, expense) => {
    acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
    return acc;
  }, {} as Record<string, number>);

  const navigateMonth = (direction: 'prev' | 'next') => {
    const date = new Date(currentMonth + '-01');
    if (direction === 'prev') {
      date.setMonth(date.getMonth() - 1);
    } else {
      date.setMonth(date.getMonth() + 1);
    }
    setCurrentMonth(date.toISOString().slice(0, 7));
  };

  const formatMonth = (monthStr: string) => {
    const date = new Date(monthStr + '-01');
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  return (
    <div className="space-y-4 pb-20">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">Personal Expenses</h1>
          <p className="text-sm text-gray-600 dark:text-gray-300">Track your personal spending month-wise</p>
        </div>
        <button
          onClick={() => setShowAddExpense(true)}
          className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors"
        >
          <Plus size={16} />
          Add Expense
        </button>
      </div>

      {/* Month Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <button
            onClick={() => navigateMonth('prev')}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <ChevronLeft size={18} className="text-gray-600 dark:text-gray-300" />
          </button>

          <div className="text-center">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{formatMonth(currentMonth)}</h2>
            <p className="text-sm text-gray-600 dark:text-gray-300">{expenses.length} expenses</p>
          </div>

          <button
            onClick={() => navigateMonth('next')}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <ChevronRight size={18} className="text-gray-600 dark:text-gray-300" />
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <Wallet size={20} className="text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-300">Total Spent</p>
              <p className="text-xl font-bold text-gray-900 dark:text-white">₹{totalAmount.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <TrendingUp size={20} className="text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-300">Avg per Day</p>
              <p className="text-xl font-bold text-gray-900 dark:text-white">
                ₹{expenses.length > 0 ? Math.round(totalAmount / new Date(currentMonth.split('-')[0], currentMonth.split('-')[1], 0).getDate()) : 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <Calendar size={20} className="text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-300">Transactions</p>
              <p className="text-xl font-bold text-gray-900 dark:text-white">{expenses.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Category Breakdown */}
      {Object.keys(categoryTotals).length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-base font-semibold text-gray-900 dark:text-white mb-3">Category Breakdown</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {Object.entries(categoryTotals)
              .sort(([,a], [,b]) => b - a)
              .map(([category, amount]) => (
                <div key={category} className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="text-2xl mb-1">{getCategoryIcon(category as any)}</div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white capitalize">{category}</div>
                  <div className="text-xs text-gray-600 dark:text-gray-300">₹{amount.toLocaleString()}</div>
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Expenses List */}
      {expenses.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center">
          <Wallet size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Expenses This Month</h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Start tracking your personal expenses for {formatMonth(currentMonth)}.
          </p>
          <button
            onClick={() => setShowAddExpense(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Add Your First Expense
          </button>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-base font-semibold text-gray-900 dark:text-white mb-3">Expenses</h3>
          <div className="space-y-2">
            {expenses.map((expense) => (
              <div key={expense.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                    <span className="text-sm">{getCategoryIcon(expense.category)}</span>
                  </div>
                  <div className="min-w-0 flex-1">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">{expense.title}</h4>
                    <div className="flex items-center gap-3 text-xs text-gray-600 dark:text-gray-300">
                      <div className="flex items-center gap-1">
                        <Calendar size={10} />
                        <span>{expense.date.toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Tag size={10} />
                        <span className="capitalize">{expense.category}</span>
                      </div>
                    </div>
                    {expense.description && (
                      <p className="text-xs text-gray-600 dark:text-gray-300 mt-1 truncate">{expense.description}</p>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="text-right">
                    <div className="text-sm font-semibold text-gray-900 dark:text-white">
                      ₹{expense.amount.toLocaleString()}
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors">
                      <Edit size={12} className="text-gray-600 dark:text-gray-300" />
                    </button>
                    <button className="p-1 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors">
                      <Trash2 size={12} className="text-red-600 dark:text-red-400" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add Expense Modal */}
      {showAddExpense && (
        <AddPersonalExpenseModal
          onClose={() => setShowAddExpense(false)}
          currentUserId={currentUser?.uid || ''}
          selectedMonth={currentMonth}
        />
      )}
    </div>
  );
};

export default PersonalExpenses;
