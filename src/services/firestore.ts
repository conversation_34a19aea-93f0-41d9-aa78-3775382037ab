import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDoc,
  getDocs,
  query,
  where,
  limit,
  onSnapshot,
  serverTimestamp,
  writeBatch,
  increment,
  Timestamp,
  DocumentReference
} from 'firebase/firestore';
import { db } from '../config/firebase';
import type {
  User,
  Group,
  ActivityLog
} from '../types';

// Collections with splitwise prefix
export const COLLECTIONS = {
  USERS: 'splitwise-users',
  GROUPS: 'splitwise-groups',
  EXPENSES: 'splitwise-expenses',
  PERSONAL_EXPENSES: 'splitwise-personalExpenses',
  ACTIVITY_LOGS: 'splitwise-activityLogs',
  BALANCES: 'splitwise-balances',
  STATISTICS: 'splitwise-statistics'
} as const;

// User operations
export const userService = {
  async create(userData: Omit<User, 'uid' | 'createdAt' | 'updatedAt'> & { uid: string }) {
    const userRef = doc(db, COLLECTIONS.USERS, userData.uid);
    await updateDoc(userRef, {
      ...userData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    return userData.uid;
  },

  async get(uid: string): Promise<User | null> {
    const userRef = doc(db, COLLECTIONS.USERS, uid);
    const userSnap = await getDoc(userRef);
    
    if (userSnap.exists()) {
      const data = userSnap.data();
      return {
        uid: userSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as User;
    }
    return null;
  },

  async update(uid: string, updates: Partial<Omit<User, 'uid' | 'createdAt'>>) {
    const userRef = doc(db, COLLECTIONS.USERS, uid);
    await updateDoc(userRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
  },

  async getAll(): Promise<User[]> {
    const usersRef = collection(db, COLLECTIONS.USERS);
    const snapshot = await getDocs(usersRef);
    
    return snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as User[];
  }
};

// Group operations
export const groupService = {
  async create(groupData: Omit<Group, 'id' | 'createdAt' | 'updatedAt'>) {
    const groupsRef = collection(db, COLLECTIONS.GROUPS);

    // Process member emails if provided
    let finalMembers = [...groupData.members];
    if (groupData.memberEmails && groupData.memberEmails.length > 0) {
      // For now, we'll store the emails and process them later
      // In a real app, you'd want to:
      // 1. Check if users with these emails exist
      // 2. Send invitations to non-existing users
      // 3. Add existing users to the group immediately

      // For this demo, we'll just store the emails for future processing
      console.log('Member emails to process:', groupData.memberEmails);
    }

    const docRef = await addDoc(groupsRef, {
      ...groupData,
      members: finalMembers,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    // Log activity
    await activityService.log({
      userId: groupData.createdBy,
      action: 'created_group',
      entityType: 'group',
      entityId: docRef.id,
      details: `Created group: ${groupData.name}`,
      metadata: {
        groupName: groupData.name,
        memberCount: finalMembers.length,
        invitedEmails: groupData.memberEmails?.length || 0
      }
    });

    return docRef.id;
  },

  async get(id: string): Promise<Group | null> {
    const groupRef = doc(db, COLLECTIONS.GROUPS, id);
    const groupSnap = await getDoc(groupRef);
    
    if (groupSnap.exists()) {
      const data = groupSnap.data();
      return {
        id: groupSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date()
      } as Group;
    }
    return null;
  },

  async getUserGroups(userId: string): Promise<Group[]> {
    const groupsRef = collection(db, COLLECTIONS.GROUPS);
    const q = query(
      groupsRef,
      where('members', 'array-contains', userId)
    );

    const snapshot = await getDocs(q);
    const groups = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date()
    })) as Group[];

    // Filter and sort in memory to avoid index requirements
    return groups
      .filter(group => group.isActive)
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  },

  async update(id: string, updates: Partial<Omit<Group, 'id' | 'createdAt'>>) {
    const groupRef = doc(db, COLLECTIONS.GROUPS, id);
    await updateDoc(groupRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
  },

  async addMember(groupId: string, userId: string) {
    const groupRef = doc(db, COLLECTIONS.GROUPS, groupId);
    const group = await this.get(groupId);
    
    if (group && !group.members.includes(userId)) {
      await updateDoc(groupRef, {
        members: [...group.members, userId],
        updatedAt: serverTimestamp()
      });
    }
  },

  async removeMember(groupId: string, userId: string) {
    const groupRef = doc(db, COLLECTIONS.GROUPS, groupId);
    const group = await this.get(groupId);
    
    if (group) {
      await updateDoc(groupRef, {
        members: group.members.filter(id => id !== userId),
        updatedAt: serverTimestamp()
      });
    }
  },

  onSnapshot(userId: string, callback: (groups: Group[]) => void) {
    const groupsRef = collection(db, COLLECTIONS.GROUPS);
    const q = query(
      groupsRef,
      where('members', 'array-contains', userId)
    );

    return onSnapshot(q, (snapshot) => {
      const groups = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date()
      })) as Group[];

      // Filter and sort in memory to avoid index requirements
      const filteredGroups = groups
        .filter(group => group.isActive)
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());

      callback(filteredGroups);
    });
  }
};

// Activity logging
export const activityService = {
  async log(activity: Omit<ActivityLog, 'id' | 'timestamp'>) {
    const logsRef = collection(db, COLLECTIONS.ACTIVITY_LOGS);
    await addDoc(logsRef, {
      ...activity,
      timestamp: serverTimestamp()
    });
  },

  async getUserActivity(userId: string, limitCount = 50): Promise<ActivityLog[]> {
    const logsRef = collection(db, COLLECTIONS.ACTIVITY_LOGS);
    const q = query(
      logsRef,
      where('userId', '==', userId),
      limit(limitCount)
    );

    const snapshot = await getDocs(q);
    const activities = snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      timestamp: doc.data().timestamp?.toDate() || new Date()
    })) as ActivityLog[];

    // Sort in memory to avoid index requirements
    return activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  },

  onSnapshot(userId: string, callback: (activities: ActivityLog[]) => void) {
    const logsRef = collection(db, COLLECTIONS.ACTIVITY_LOGS);
    const q = query(
      logsRef,
      where('userId', '==', userId),
      limit(50)
    );

    return onSnapshot(q, (snapshot) => {
      const activities = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date()
      })) as ActivityLog[];

      // Sort in memory to avoid index requirements
      const sortedActivities = activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      callback(sortedActivities);
    });
  }
};

// Utility functions
export const firestoreUtils = {
  async batchWrite(operations: Array<{ type: 'set' | 'update' | 'delete', ref: DocumentReference, data?: object }>) {
    const batch = writeBatch(db);
    
    operations.forEach(({ type, ref, data }) => {
      switch (type) {
        case 'set':
          if (data) batch.set(ref, data);
          break;
        case 'update':
          if (data) batch.update(ref, data);
          break;
        case 'delete':
          batch.delete(ref);
          break;
      }
    });
    
    await batch.commit();
  },

  serverTimestamp,
  increment,
  
  // Convert Firestore timestamp to Date
  timestampToDate(timestamp: Timestamp | null): Date {
    return timestamp ? timestamp.toDate() : new Date();
  }
};
