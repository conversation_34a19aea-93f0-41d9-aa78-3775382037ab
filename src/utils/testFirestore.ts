import { doc, setDoc, getDoc, collection, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';
import { COLLECTIONS } from '../services/firestore';

export const testFirestoreConnection = async () => {
  try {
    console.log('Testing Firestore connection with splitwise collections...');

    // Test writing to splitwise-users collection
    const testRef = doc(db, COLLECTIONS.USERS, 'test-connection');
    await setDoc(testRef, {
      email: '<EMAIL>',
      displayName: 'Test User',
      isAdmin: false,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    console.log('✅ Successfully wrote to splitwise-users collection');

    // Try to read the document back
    const docSnap = await getDoc(testRef);
    if (docSnap.exists()) {
      console.log('✅ Successfully read from splitwise-users:', docSnap.data());
      return true;
    } else {
      console.log('❌ Document does not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ Firestore connection failed:', error);
    return false;
  }
};

export const checkUserCollections = async () => {
  try {
    console.log('🔍 Checking user collections...');

    // Check old users collection
    const oldUsersRef = collection(db, 'users');
    const oldUsersSnap = await getDocs(oldUsersRef);
    console.log(`📊 Old 'users' collection has ${oldUsersSnap.size} documents`);
    oldUsersSnap.forEach(doc => {
      console.log('Old user:', doc.id, doc.data());
    });

    // Check new splitwise-users collection
    const newUsersRef = collection(db, COLLECTIONS.USERS);
    const newUsersSnap = await getDocs(newUsersRef);
    console.log(`📊 New 'splitwise-users' collection has ${newUsersSnap.size} documents`);
    newUsersSnap.forEach(doc => {
      console.log('New user:', doc.id, doc.data());
    });

  } catch (error) {
    console.error('❌ Error checking collections:', error);
  }
};

// Call this function to test the connection
if (typeof window !== 'undefined') {
  // Only run in browser environment
  setTimeout(() => {
    testFirestoreConnection();
    checkUserCollections();
  }, 2000);
}
