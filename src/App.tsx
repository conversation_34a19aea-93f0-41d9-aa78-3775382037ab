
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import Layout from './components/Layout/Layout';
import Login from './components/Auth/Login';
import Groups from './components/Groups/Groups';
import GroupDetails from './components/Groups/GroupDetails';
import SingleExpense from './components/Expenses/SingleExpense';
import PersonalExpenses from './components/Personal/PersonalExpenses';
import ActivityLog from './components/Activity/ActivityLog';
import Account from './components/Account/Account';
import AdminPanel from './components/Admin/AdminPanel';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import AdminRoute from './components/Auth/AdminRoute';

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <div className="App min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100">
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/" element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }>
                <Route index element={<Groups />} />
                <Route path="groups" element={<Groups />} />
                <Route path="groups/:groupId" element={<GroupDetails />} />
                <Route path="single" element={<SingleExpense />} />
                <Route path="personal" element={<PersonalExpenses />} />
                <Route path="activity" element={<ActivityLog />} />
                <Route path="account" element={<Account />} />
              </Route>
              <Route path="/admin" element={
                <AdminRoute>
                  <AdminPanel />
                </AdminRoute>
              } />
            </Routes>
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
