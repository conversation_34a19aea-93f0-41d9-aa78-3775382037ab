import React, { createContext, useContext, useEffect, useState } from 'react';
import {
  type User as FirebaseUser,
  signInWithPopup,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  updateProfile,
  signOut,
  onAuthStateChanged
} from 'firebase/auth';
import { doc, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';
import { auth, googleProvider, db } from '../config/firebase';
import { COLLECTIONS } from '../services/firestore';
import type { User } from '../types';

interface AuthContextType {
  currentUser: User | null;
  loginWithGoogle: () => Promise<void>;
  loginWithEmail: (email: string, password: string) => Promise<void>;
  signupWithEmail: (email: string, password: string, displayName: string) => Promise<void>;
  logout: () => Promise<void>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

const ADMIN_EMAIL = import.meta.env.VITE_ADMIN_EMAIL || '<EMAIL>';

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const createUserDocument = async (firebaseUser: FirebaseUser): Promise<User> => {
    try {
      console.log('📍 Checking collection path:', COLLECTIONS.USERS);
      const userRef = doc(db, COLLECTIONS.USERS, firebaseUser.uid);
      console.log('📍 User reference created for UID:', firebaseUser.uid);

      const userSnap = await getDoc(userRef);
      console.log('📍 Document exists check completed');

      if (!userSnap.exists()) {
        console.log('📍 Creating new user document...');
        const userData: Omit<User, 'uid'> = {
          email: firebaseUser.email!,
          displayName: firebaseUser.displayName || firebaseUser.email!.split('@')[0],
          photoURL: firebaseUser.photoURL || undefined,
          isAdmin: firebaseUser.email === ADMIN_EMAIL,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        console.log('📍 User data prepared:', userData);

        try {
          console.log('📍 About to call setDoc...');

          // Try with regular Date objects first (simpler)
          const docData = {
            ...userData,
            createdAt: new Date(),
            updatedAt: new Date()
          };

          console.log('📍 Document data to write:', docData);

          await setDoc(userRef, docData);
          console.log('✅ User document created successfully in Firestore');
        } catch (setDocError: any) {
          console.error('❌ setDoc failed:', setDocError);
          console.error('❌ Error details:', {
            code: setDocError?.code,
            message: setDocError?.message,
            stack: setDocError?.stack
          });
          throw setDocError;
        }
        return { uid: firebaseUser.uid, ...userData };
      } else {
        console.log('📍 User document already exists, returning existing data');
        const userData = userSnap.data() as Omit<User, 'uid'>;
        return { uid: firebaseUser.uid, ...userData };
      }
    } catch (error) {
      console.error('❌ Error in createUserDocument:', error);
      throw error;
    }
  };

  const loginWithGoogle = async () => {
    try {
      const result = await signInWithPopup(auth, googleProvider);
      const user = await createUserDocument(result.user);
      setCurrentUser(user);
    } catch (error) {
      console.error('Google login error:', error);
      throw error;
    }
  };

  const loginWithEmail = async (email: string, password: string) => {
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      const user = await createUserDocument(result.user);
      setCurrentUser(user);
    } catch (error) {
      console.error('Email login error:', error);
      throw error;
    }
  };

  const signupWithEmail = async (email: string, password: string, displayName: string) => {
    try {
      console.log('🔄 Creating user with email:', email);
      const result = await createUserWithEmailAndPassword(auth, email, password);
      console.log('✅ Firebase Auth user created:', result.user.uid);

      // Update the user's display name
      await updateProfile(result.user, {
        displayName: displayName
      });
      console.log('✅ Display name updated');

      // Create user document with updated info
      const updatedUser = {
        ...result.user,
        displayName: displayName
      } as FirebaseUser;

      console.log('🔄 Creating Firestore document...');
      const user = await createUserDocument(updatedUser);
      console.log('✅ Firestore document created successfully');
      setCurrentUser(user);
    } catch (error) {
      console.error('❌ Email signup error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
      setCurrentUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        try {
          const user = await createUserDocument(firebaseUser);
          setCurrentUser(user);
        } catch (error) {
          console.error('Error creating user document:', error);
          setCurrentUser(null);
        }
      } else {
        setCurrentUser(null);
      }
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    loginWithGoogle,
    loginWithEmail,
    signupWithEmail,
    logout,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
